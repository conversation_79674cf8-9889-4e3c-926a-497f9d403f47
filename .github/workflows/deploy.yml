name: "♻️ | Deploy Using <PERSON><PERSON> and <PERSON><PERSON>"

on:
  workflow_call:

env:
  MODULE_IDENTIFIERS: |
    {
      "nxt-sh-core": "core",
      "nxt-sh-terra-backend": "terra",
      "nxt-sh-jobs-backend": "jobs",
      "nxt-sh-sample-flask": "sample_flask",
      "nxt-sh-storage-backend": "storage",
      "nxt-sh-auth-middleware-backend": "auth_middleware",
    }

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name == 'production' && 'Production' || github.ref_name }}
    outputs:
      short_sha: ${{ steps.get_sha.outputs.sha7 }}
      start_time: ${{ steps.set_time.outputs.start_time }}
    steps:
      - name: Set start time
        id: set_time
        run: |
          START_TIME=$(date +%s)
          echo "start_time=${START_TIME}" >> $GITHUB_OUTPUT

      - name: Get short SHA
        id: get_sha
        run: |
          echo "Full SHA: ${GITHUB_SHA}"
          SHORT_SHA=$(echo ${GITHUB_SHA} | cut -c1-7)
          echo "Short SHA: ${SHORT_SHA}"
          echo "sha7=${SHORT_SHA}" >> $GITHUB_OUTPUT

      - name: Get module name
        id: module-id
        run: |
          REPO_NAME=$(echo "${{ github.repository }}" | cut -d '/' -f 2)
          MODULE_MAP='${{ env.MODULE_IDENTIFIERS }}'
          MODULE_ID=$(echo "$MODULE_MAP" | jq -r --arg repo "$REPO_NAME" '.[$repo]')
          if [[ "$MODULE_ID" == "null" || -z "$MODULE_ID" ]]; then
            echo "Error: No module identifier found for repository $REPO_NAME"
            exit 1
          fi
          echo "module-id=${MODULE_ID}" >> $GITHUB_OUTPUT

      - name: Checkout workflows repository
        uses: actions/checkout@v4
        with:
          repository: sensehawk/nxt-sh-workflows
          path: .github/nxt-sh-workflows
          token: ${{ secrets.MAINTENANCE_PAT }}

      - name: Build and push Docker image
        uses: ./.github/nxt-sh-workflows/.github/actions/docker-build-push
        with:
          docker-image-name: "sensehawk/nxt_sh_${{ steps.module-id.outputs.module-id }}"
          short-commit-hash: ${{ steps.get_sha.outputs.sha7 }}
          dockerhub-username: ${{ secrets.NXT_DOCKER_USERNAME }}
          dockerhub-token: ${{ secrets.NXT_DOCKER_TOKEN }}
          github-server-url: ${{ github.server_url }}
          github-repository: ${{ github.repository }}
          github-sha: ${{ github.sha }}

      - name: Invoke Lambda function
        uses: ./.github/nxt-sh-workflows/.github/actions/lambda-invoke
        with:
          aws-access-key: ${{ secrets.NXT_SH_AWS_LAMBDA_ACCESS_KEY }}
          aws-secret-key: ${{ secrets.NXT_SH_AWS_LAMBDA_SECRET_ACCESS_KEY }}
          aws-region: "us-east-1"
          function-name: "nxt-sh-deploy_nomad_job-lambda"
          job-name: "nxt_sh_${{ steps.module-id.outputs.module-id }}"
          short-commit-hash: ${{ steps.get_sha.outputs.sha7 }}
